//
//  ErrorHandling.swift
//  Lmini
//
//  Created by MacBook Pro-Leo on 5/30/25.
//

import Foundation
import SwiftUI
import UserNotifications

// MARK: - App Error Types

enum AppError: Error, LocalizedError {
    case imageProcessingFailed(String)
    case networkError(String)
    case subscriptionRequired
    case dailyLimitExceeded
    case invalidImageFormat
    case imageTooLarge
    case apiKeyMissing
    case processingTimeout
    case storageError(String)
    case unknownError(String)
    
    var errorDescription: String? {
        switch self {
        case .imageProcessingFailed(let message):
            return "Image processing failed: \(message)"
        case .networkError(let message):
            return "Network error: \(message)"
        case .subscriptionRequired:
            return "This feature requires a subscription"
        case .dailyLimitExceeded:
            return "Daily processing limit exceeded. Upgrade to continue."
        case .invalidImageFormat:
            return "Invalid image format. Please select a valid image."
        case .imageTooLarge:
            return "Image is too large. Please select a smaller image."
        case .apiKeyMissing:
            return "API key is missing. Please configure in settings."
        case .processingTimeout:
            return "Processing timeout. Please try again."
        case .storageError(let message):
            return "Storage error: \(message)"
        case .unknownError(let message):
            return "Unknown error: \(message)"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .imageProcessingFailed:
            return "Try selecting a different image or check your internet connection."
        case .networkError:
            return "Check your internet connection and try again."
        case .subscriptionRequired:
            return "Upgrade to premium to access this feature."
        case .dailyLimitExceeded:
            return "Upgrade to premium for unlimited processing."
        case .invalidImageFormat:
            return "Select a JPEG or PNG image."
        case .imageTooLarge:
            return "Resize your image or select a smaller one."
        case .apiKeyMissing:
            return "Go to Settings and configure your API key."
        case .processingTimeout:
            return "Check your internet connection and try again."
        case .storageError:
            return "Free up some storage space and try again."
        case .unknownError:
            return "Restart the app and try again."
        }
    }
}

// MARK: - Error Handler

class ErrorHandler: ObservableObject {
    static let shared = ErrorHandler()
    
    @Published var currentError: AppError?
    @Published var showError = false
    
    private init() {}
    
    func handle(_ error: Error) {
        DispatchQueue.main.async {
            if let appError = error as? AppError {
                self.currentError = appError
            } else if let enhancementError = error as? AIImageService.EnhancementError {
                self.currentError = self.convertEnhancementError(enhancementError)
            } else {
                self.currentError = .unknownError(error.localizedDescription)
            }
            
            self.showError = true
            self.logError(self.currentError!)
        }
    }
    
    private func convertEnhancementError(_ error: AIImageService.EnhancementError) -> AppError {
        switch error {
        case .invalidImage:
            return .invalidImageFormat
        case .networkError:
            return .networkError("Failed to connect to processing service")
        case .apiError(let message):
            return .imageProcessingFailed(message)
        case .decodingError:
            return .imageProcessingFailed("Failed to process response")
        case .processingTimeout:
            return .processingTimeout
        }
    }
    
    private func logError(_ error: AppError) {
        print("🔴 Error: \(error.localizedDescription)")
        if let suggestion = error.recoverySuggestion {
            print("💡 Suggestion: \(suggestion)")
        }
        
        // 这里可以添加崩溃报告服务，如 Firebase Crashlytics
        // Crashlytics.crashlytics().record(error: error)
    }
    
    func clearError() {
        DispatchQueue.main.async {
            self.currentError = nil
            self.showError = false
        }
    }
}

// MARK: - Notification Manager

class NotificationManager: ObservableObject {
    static let shared = NotificationManager()
    
    @Published var hasPermission = false
    
    private init() {
        checkPermission()
    }
    
    func requestPermission() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .badge, .sound]) { granted, error in
            DispatchQueue.main.async {
                self.hasPermission = granted
            }
            
            if let error = error {
                print("Notification permission error: \(error)")
            }
        }
    }
    
    private func checkPermission() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                self.hasPermission = settings.authorizationStatus == .authorized
            }
        }
    }
    
    func scheduleProcessingCompleteNotification(for filterType: AIImageService.FilterType) {
        guard hasPermission else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "Processing Complete"
        content.body = "Your \(filterType.displayName) is ready!"
        content.sound = .default
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        let request = UNNotificationRequest(identifier: UUID().uuidString, content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Failed to schedule notification: \(error)")
            }
        }
    }
    
    func scheduleDailyLimitNotification() {
        guard hasPermission else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "Daily Limit Reached"
        content.body = "You've reached your daily processing limit. Upgrade to premium for unlimited access!"
        content.sound = .default
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        let request = UNNotificationRequest(identifier: "daily-limit", content: content, trigger: trigger)
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Failed to schedule notification: \(error)")
            }
        }
    }
}

// MARK: - Error Alert View

struct ErrorAlertView: ViewModifier {
    @ObservedObject var errorHandler = ErrorHandler.shared
    
    func body(content: Content) -> some View {
        content
            .alert("Error", isPresented: $errorHandler.showError) {
                Button("OK") {
                    errorHandler.clearError()
                }
                
                if let error = errorHandler.currentError,
                   error.recoverySuggestion != nil {
                    Button("Help") {
                        // 可以打开帮助页面或设置
                    }
                }
            } message: {
                if let error = errorHandler.currentError {
                    VStack(alignment: .leading) {
                        Text(error.localizedDescription)
                        
                        if let suggestion = error.recoverySuggestion {
                            Text(suggestion)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
    }
}

extension View {
    func errorAlert() -> some View {
        modifier(ErrorAlertView())
    }
}

// MARK: - Loading State Manager

class LoadingStateManager: ObservableObject {
    static let shared = LoadingStateManager()
    
    @Published var isLoading = false
    @Published var loadingMessage = ""
    @Published var progress: Double = 0.0
    
    private init() {}
    
    func startLoading(message: String = "Processing...") {
        DispatchQueue.main.async {
            self.isLoading = true
            self.loadingMessage = message
            self.progress = 0.0
        }
    }
    
    func updateProgress(_ progress: Double, message: String? = nil) {
        DispatchQueue.main.async {
            self.progress = progress
            if let message = message {
                self.loadingMessage = message
            }
        }
    }
    
    func stopLoading() {
        DispatchQueue.main.async {
            self.isLoading = false
            self.loadingMessage = ""
            self.progress = 0.0
        }
    }
}

// MARK: - Loading Overlay View

struct LoadingOverlayView: View {
    @ObservedObject var loadingState = LoadingStateManager.shared
    
    var body: some View {
        if loadingState.isLoading {
            ZStack {
                Color.black.opacity(0.3)
                    .ignoresSafeArea()
                
                VStack(spacing: 20) {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(1.5)
                    
                    Text(loadingState.loadingMessage)
                        .foregroundColor(.white)
                        .font(.headline)
                    
                    if loadingState.progress > 0 {
                        ProgressView(value: loadingState.progress)
                            .progressViewStyle(LinearProgressViewStyle(tint: .white))
                            .frame(width: 200)
                        
                        Text("\(Int(loadingState.progress * 100))%")
                            .foregroundColor(.white)
                            .font(.caption)
                    }
                }
                .padding(30)
                .background(Color.black.opacity(0.7))
                .clipShape(RoundedRectangle(cornerRadius: 16))
            }
        }
    }
}

extension View {
    func loadingOverlay() -> some View {
        ZStack {
            self
            LoadingOverlayView()
        }
    }
}
