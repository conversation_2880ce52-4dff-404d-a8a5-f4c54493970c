//
//  AppConfiguration.swift
//  Lmini
//
//  Created by <PERSON><PERSON><PERSON> Pro-Leo on 5/30/25.
//

import Foundation
import SwiftUI

// MARK: - App Configuration Manager

class AppConfiguration: ObservableObject {
    static let shared = AppConfiguration()
    
    // MARK: - API Settings
    @Published var apiProvider: AIImageService.APIProvider = .mock
    @Published var deepAIAPIKey: String = ""
    @Published var letsEnhanceAPIKey: String = ""
    
    // MARK: - Image Processing Settings
    @Published var imageQuality: Double = 0.8
    @Published var maxImageSize: CGSize = CGSize(width: 2048, height: 2048)
    @Published var enableImageCompression: Bool = true
    
    // MARK: - User Preferences
    @Published var autoSaveToPhotos: Bool = false
    @Published var enableHapticFeedback: Bool = true
    @Published var enableNotifications: Bool = true
    @Published var preferredLanguage: String = "en"
    
    // MARK: - UI Settings
    @Published var enableDarkMode: Bool = false
    @Published var showProcessingDetails: Bool = true
    @Published var enableAnimations: Bool = true
    
    // MARK: - Performance Settings
    @Published var maxConcurrentProcessing: Int = 3
    @Published var enableBackgroundProcessing: Bool = false
    @Published var cacheProcessedImages: Bool = true
    
    // MARK: - Subscription Settings
    @Published var isSubscribed: Bool = false
    @Published var subscriptionType: SubscriptionType = .free
    @Published var subscriptionExpiryDate: Date?
    
    private let userDefaults = UserDefaults.standard
    
    private init() {
        loadConfiguration()
    }
    
    // MARK: - Subscription Types
    
    enum SubscriptionType: String, CaseIterable {
        case free = "free"
        case weekly = "weekly"
        case monthly = "monthly"
        case yearly = "yearly"
        
        var displayName: String {
            switch self {
            case .free: return "Free"
            case .weekly: return "Weekly"
            case .monthly: return "Monthly"
            case .yearly: return "Yearly"
            }
        }
        
        var price: String {
            switch self {
            case .free: return "Free"
            case .weekly: return "$2.99/week"
            case .monthly: return "$9.99/month"
            case .yearly: return "$59.99/year"
            }
        }
        
        var features: [String] {
            switch self {
            case .free:
                return [
                    "Basic enhancement",
                    "3 images per day",
                    "Standard quality"
                ]
            case .weekly, .monthly, .yearly:
                return [
                    "All AI filters",
                    "Unlimited processing",
                    "High quality output",
                    "Batch processing",
                    "Priority support",
                    "No watermarks"
                ]
            }
        }
    }
    
    // MARK: - Supported Languages
    
    static let supportedLanguages = [
        "en": "English",
        "hi": "Hindi",
        "ja": "Japanese",
        "ko": "Korean",
        "pt": "Portuguese",
        "ru": "Russian",
        "zh-Hans": "Simplified Chinese",
        "zh-Hant": "Traditional Chinese",
        "es": "Spanish",
        "th": "Thai"
    ]
    
    // MARK: - Configuration Methods
    
    func saveConfiguration() {
        userDefaults.set(apiProvider.rawValue, forKey: "apiProvider")
        userDefaults.set(deepAIAPIKey, forKey: "deepAIAPIKey")
        userDefaults.set(letsEnhanceAPIKey, forKey: "letsEnhanceAPIKey")
        
        userDefaults.set(imageQuality, forKey: "imageQuality")
        userDefaults.set(NSCoder.string(for: maxImageSize), forKey: "maxImageSize")
        userDefaults.set(enableImageCompression, forKey: "enableImageCompression")
        
        userDefaults.set(autoSaveToPhotos, forKey: "autoSaveToPhotos")
        userDefaults.set(enableHapticFeedback, forKey: "enableHapticFeedback")
        userDefaults.set(enableNotifications, forKey: "enableNotifications")
        userDefaults.set(preferredLanguage, forKey: "preferredLanguage")
        
        userDefaults.set(enableDarkMode, forKey: "enableDarkMode")
        userDefaults.set(showProcessingDetails, forKey: "showProcessingDetails")
        userDefaults.set(enableAnimations, forKey: "enableAnimations")
        
        userDefaults.set(maxConcurrentProcessing, forKey: "maxConcurrentProcessing")
        userDefaults.set(enableBackgroundProcessing, forKey: "enableBackgroundProcessing")
        userDefaults.set(cacheProcessedImages, forKey: "cacheProcessedImages")
        
        userDefaults.set(isSubscribed, forKey: "isSubscribed")
        userDefaults.set(subscriptionType.rawValue, forKey: "subscriptionType")
        userDefaults.set(subscriptionExpiryDate, forKey: "subscriptionExpiryDate")
    }
    
    private func loadConfiguration() {
        // API Settings
        if let providerString = userDefaults.string(forKey: "apiProvider"),
           let provider = AIImageService.APIProvider(rawValue: providerString) {
            apiProvider = provider
        }
        deepAIAPIKey = userDefaults.string(forKey: "deepAIAPIKey") ?? ""
        letsEnhanceAPIKey = userDefaults.string(forKey: "letsEnhanceAPIKey") ?? ""
        
        // Image Processing Settings
        imageQuality = userDefaults.double(forKey: "imageQuality")
        if imageQuality == 0 { imageQuality = 0.8 }
        
        if let sizeString = userDefaults.string(forKey: "maxImageSize") {
            maxImageSize = NSCoder.cgSize(for: sizeString)
        }
        enableImageCompression = userDefaults.bool(forKey: "enableImageCompression")
        
        // User Preferences
        autoSaveToPhotos = userDefaults.bool(forKey: "autoSaveToPhotos")
        enableHapticFeedback = userDefaults.bool(forKey: "enableHapticFeedback")
        enableNotifications = userDefaults.bool(forKey: "enableNotifications")
        preferredLanguage = userDefaults.string(forKey: "preferredLanguage") ?? "en"
        
        // UI Settings
        enableDarkMode = userDefaults.bool(forKey: "enableDarkMode")
        showProcessingDetails = userDefaults.bool(forKey: "showProcessingDetails")
        enableAnimations = userDefaults.bool(forKey: "enableAnimations")
        
        // Performance Settings
        maxConcurrentProcessing = userDefaults.integer(forKey: "maxConcurrentProcessing")
        if maxConcurrentProcessing == 0 { maxConcurrentProcessing = 3 }
        
        enableBackgroundProcessing = userDefaults.bool(forKey: "enableBackgroundProcessing")
        cacheProcessedImages = userDefaults.bool(forKey: "cacheProcessedImages")
        
        // Subscription Settings
        isSubscribed = userDefaults.bool(forKey: "isSubscribed")
        if let typeString = userDefaults.string(forKey: "subscriptionType"),
           let type = SubscriptionType(rawValue: typeString) {
            subscriptionType = type
        }
        subscriptionExpiryDate = userDefaults.object(forKey: "subscriptionExpiryDate") as? Date
    }
    
    // MARK: - Validation Methods
    
    func validateAPIKeys() -> Bool {
        switch apiProvider {
        case .deepAI:
            return !deepAIAPIKey.isEmpty
        case .letsEnhance:
            return !letsEnhanceAPIKey.isEmpty
        case .mock:
            return true
        }
    }
    
    func canProcessImage() -> Bool {
        if subscriptionType == .free {
            // 检查免费用户的限制
            let today = Calendar.current.startOfDay(for: Date())
            let todayItems = HistoryManager.shared.historyItems.filter {
                Calendar.current.startOfDay(for: $0.timestamp) == today
            }
            return todayItems.count < 3
        }
        return true
    }
    
    func getRemainingFreeProcesses() -> Int {
        if subscriptionType == .free {
            let today = Calendar.current.startOfDay(for: Date())
            let todayItems = HistoryManager.shared.historyItems.filter {
                Calendar.current.startOfDay(for: $0.timestamp) == today
            }
            return max(0, 3 - todayItems.count)
        }
        return Int.max
    }
    
    // MARK: - Reset Methods
    
    func resetToDefaults() {
        apiProvider = .mock
        deepAIAPIKey = ""
        letsEnhanceAPIKey = ""
        
        imageQuality = 0.8
        maxImageSize = CGSize(width: 2048, height: 2048)
        enableImageCompression = true
        
        autoSaveToPhotos = false
        enableHapticFeedback = true
        enableNotifications = true
        preferredLanguage = "en"
        
        enableDarkMode = false
        showProcessingDetails = true
        enableAnimations = true
        
        maxConcurrentProcessing = 3
        enableBackgroundProcessing = false
        cacheProcessedImages = true
        
        saveConfiguration()
    }
}

// MARK: - API Provider Extension

extension AIImageService.APIProvider {
    var rawValue: String {
        switch self {
        case .deepAI: return "deepAI"
        case .letsEnhance: return "letsEnhance"
        case .mock: return "mock"
        }
    }
    
    init?(rawValue: String) {
        switch rawValue {
        case "deepAI": self = .deepAI
        case "letsEnhance": self = .letsEnhance
        case "mock": self = .mock
        default: return nil
        }
    }
}
