//
//  SupportingViews.swift
//  Lmini
//
//  Created by <PERSON><PERSON><PERSON> Pro-Leo on 5/30/25.
//

import SwiftUI
import UIKit
import PhotosUI

// MARK: - Image Picker

struct ImagePicker: UIViewControllerRepresentable {
    @Binding var image: UIImage?
    @Environment(\.presentationMode) var presentationMode
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        picker.allowsEditing = false
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let uiImage = info[.originalImage] as? UIImage {
                parent.image = uiImage
            }
            parent.presentationMode.wrappedValue.dismiss()
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}

// MARK: - Multi Image Picker

struct MultiImagePicker: UIViewControllerRepresentable {
    @Binding var images: [UIImage]
    @Environment(\.presentationMode) var presentationMode
    
    func makeUIViewController(context: Context) -> PHPickerViewController {
        var config = PHPickerConfiguration()
        config.filter = .images
        config.selectionLimit = 10 // 最多选择10张图片
        
        let picker = PHPickerViewController(configuration: config)
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: PHPickerViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, PHPickerViewControllerDelegate {
        let parent: MultiImagePicker
        
        init(_ parent: MultiImagePicker) {
            self.parent = parent
        }
        
        func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
            parent.presentationMode.wrappedValue.dismiss()
            
            guard !results.isEmpty else { return }
            
            var newImages: [UIImage] = []
            let group = DispatchGroup()
            
            for result in results {
                group.enter()
                result.itemProvider.loadObject(ofClass: UIImage.self) { (object, error) in
                    defer { group.leave() }
                    
                    if let image = object as? UIImage {
                        newImages.append(image)
                    }
                }
            }
            
            group.notify(queue: .main) {
                self.parent.images.append(contentsOf: newImages)
            }
        }
    }
}

// MARK: - Share Sheet

struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(
            activityItems: activityItems,
            applicationActivities: nil
        )
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - Enhanced Main View

struct EnhancedMainView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            FilterSelectionView()
                .tabItem {
                    Image(systemName: "wand.and.stars")
                    Text("Filters")
                }
                .tag(0)
            
            BatchProcessingView()
                .tabItem {
                    Image(systemName: "photo.stack")
                    Text("Batch")
                }
                .tag(1)
            
            HistoryView()
                .tabItem {
                    Image(systemName: "clock.arrow.circlepath")
                    Text("History")
                }
                .tag(2)
            
            SettingsView()
                .tabItem {
                    Image(systemName: "gear")
                    Text("Settings")
                }
                .tag(3)
        }
        .accentColor(.blue)
    }
}

// MARK: - Enhanced History View

struct HistoryView: View {
    @StateObject private var historyManager = HistoryManager.shared
    @State private var showShareSheet = false
    @State private var selectedImage: UIImage?
    
    var body: some View {
        NavigationView {
            Group {
                if historyManager.historyItems.isEmpty {
                    VStack {
                        Image(systemName: "clock.arrow.circlepath")
                            .font(.system(size: 50))
                            .foregroundColor(.gray)
                        Text("No history yet")
                            .font(.headline)
                            .foregroundColor(.gray)
                        Text("Process some images to see them here")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                } else {
                    List {
                        ForEach(historyManager.historyItems) { item in
                            HistoryItemRow(item: item) { image in
                                selectedImage = image
                                showShareSheet = true
                            }
                        }
                        .onDelete(perform: deleteItems)
                    }
                }
            }
            .navigationTitle("History")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                if !historyManager.historyItems.isEmpty {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("Clear All") {
                            historyManager.clearHistory()
                        }
                        .foregroundColor(.red)
                    }
                }
            }
            .sheet(isPresented: $showShareSheet) {
                if let image = selectedImage {
                    ShareSheet(activityItems: [image])
                }
            }
        }
    }
    
    private func deleteItems(offsets: IndexSet) {
        for index in offsets {
            let item = historyManager.historyItems[index]
            historyManager.removeHistoryItem(item)
        }
    }
}

// MARK: - History Item Row

struct HistoryItemRow: View {
    let item: HistoryItem
    let onShare: (UIImage) -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 缩略图
            if let imageData = item.processedImageData,
               let image = UIImage(data: imageData) {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 60, height: 60)
                    .clipShape(RoundedRectangle(cornerRadius: 8))
            } else {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 60, height: 60)
                    .overlay(
                        Image(systemName: "photo")
                            .foregroundColor(.gray)
                    )
            }
            
            // 信息
            VStack(alignment: .leading, spacing: 4) {
                Text(item.filterType.displayName)
                    .font(.headline)
                
                Text(item.timestamp, style: .date)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(item.timestamp, style: .time)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 分享按钮
            Button(action: {
                if let imageData = item.processedImageData,
                   let image = UIImage(data: imageData) {
                    onShare(image)
                }
            }) {
                Image(systemName: "square.and.arrow.up")
                    .foregroundColor(.blue)
            }
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Settings View

struct SettingsView: View {
    @AppStorage("apiProvider") private var apiProvider: String = "mock"
    @AppStorage("imageQuality") private var imageQuality: Double = 0.8
    @AppStorage("autoSaveToPhotos") private var autoSaveToPhotos: Bool = false
    @AppStorage("enableHapticFeedback") private var enableHapticFeedback: Bool = true
    
    var body: some View {
        NavigationView {
            Form {
                Section("Processing") {
                    Picker("API Provider", selection: $apiProvider) {
                        Text("Mock (Testing)").tag("mock")
                        Text("DeepAI").tag("deepai")
                        Text("Let's Enhance").tag("letsenhance")
                    }
                    
                    VStack(alignment: .leading) {
                        Text("Image Quality: \(Int(imageQuality * 100))%")
                        Slider(value: $imageQuality, in: 0.1...1.0, step: 0.1)
                    }
                }
                
                Section("Preferences") {
                    Toggle("Auto Save to Photos", isOn: $autoSaveToPhotos)
                    Toggle("Haptic Feedback", isOn: $enableHapticFeedback)
                }
                
                Section("About") {
                    HStack {
                        Text("Version")
                        Spacer()
                        Text("1.0.0")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Build")
                        Spacer()
                        Text("1")
                            .foregroundColor(.secondary)
                    }
                }
                
                Section("Support") {
                    Button("Rate App") {
                        // 实现评分功能
                    }
                    
                    Button("Contact Support") {
                        // 实现联系支持功能
                    }
                    
                    Button("Privacy Policy") {
                        // 实现隐私政策功能
                    }
                }
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.large)
        }
    }
}

// MARK: - Preview

struct EnhancedMainView_Previews: PreviewProvider {
    static var previews: some View {
        EnhancedMainView()
    }
}
