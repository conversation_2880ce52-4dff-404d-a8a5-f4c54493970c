//
//  LminiApp.swift
//  Lmini
//
//  Created by MacBook Pro-Leo on 5/30/25.
//

import SwiftUI

@main
struct LminiApp: App {
    @StateObject private var appConfiguration = AppConfiguration.shared
    @StateObject private var historyManager = HistoryManager.shared
    @StateObject private var errorHandler = ErrorHandler.shared
    @StateObject private var notificationManager = NotificationManager.shared
    @StateObject private var loadingState = LoadingStateManager.shared
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(appConfiguration)
                .environmentObject(historyManager)
                .environmentObject(errorHandler)
                .environmentObject(notificationManager)
                .environmentObject(loadingState)
                .errorAlert()
                .loadingOverlay()
                .onAppear {
                    setupApp()
                }
                .preferredColorScheme(appConfiguration.enableDarkMode ? .dark : .light)
        }
    }
    
    private func setupApp() {
        // 请求通知权限
        if appConfiguration.enableNotifications {
            notificationManager.requestPermission()
        }
        
        // 保存配置
        appConfiguration.saveConfiguration()
        
        // 设置全局外观
        setupAppearance()
    }
    
    private func setupAppearance() {
        // 设置导航栏外观
        let appearance = UINavigationBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor.systemBackground
        appearance.titleTextAttributes = [.foregroundColor: UIColor.label]
        appearance.largeTitleTextAttributes = [.foregroundColor: UIColor.label]
        
        UINavigationBar.appearance().standardAppearance = appearance
        UINavigationBar.appearance().compactAppearance = appearance
        UINavigationBar.appearance().scrollEdgeAppearance = appearance
        
        // 设置标签栏外观
        let tabBarAppearance = UITabBarAppearance()
        tabBarAppearance.configureWithOpaqueBackground()
        tabBarAppearance.backgroundColor = UIColor.systemBackground
        
        UITabBar.appearance().standardAppearance = tabBarAppearance
        UITabBar.appearance().scrollEdgeAppearance = tabBarAppearance
    }
}

// MARK: - Content View

struct ContentView: View {
    @EnvironmentObject var appConfiguration: AppConfiguration
    @State private var showSplash = true
    @State private var showOnboarding = false
    
    var body: some View {
        Group {
            if showSplash {
                SplashView {
                    withAnimation(.easeInOut(duration: 0.5)) {
                        showSplash = false
                        checkOnboardingStatus()
                    }
                }
            } else if showOnboarding {
                OnboardingView {
                    withAnimation(.easeInOut(duration: 0.5)) {
                        showOnboarding = false
                        UserDefaults.standard.set(true, forKey: "hasSeenOnboarding")
                    }
                }
            } else {
                MainAppView()
            }
        }
    }
    
    private func checkOnboardingStatus() {
        let hasSeenOnboarding = UserDefaults.standard.bool(forKey: "hasSeenOnboarding")
        if !hasSeenOnboarding {
            showOnboarding = true
        }
    }
}

// MARK: - Main App View

struct MainAppView: View {
    @EnvironmentObject var appConfiguration: AppConfiguration
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // 主功能页面 - 使用原型设计
            PrototypeMainView()
                .tabItem {
                    Image(systemName: "wand.and.stars")
                    Text("AI Filters")
                }
                .tag(0)
            
            // 批量处理
            BatchProcessingView()
                .tabItem {
                    Image(systemName: "photo.stack")
                    Text("Batch")
                }
                .tag(1)
            
            // 历史记录
            EnhancedHistoryView()
                .tabItem {
                    Image(systemName: "clock.arrow.circlepath")
                    Text("History")
                }
                .tag(2)
            
            // 设置
            EnhancedSettingsView()
                .tabItem {
                    Image(systemName: "gear")
                    Text("Settings")
                }
                .tag(3)
        }
        .accentColor(.blue)
    }
}

// MARK: - Splash View

struct SplashView: View {
    let onComplete: () -> Void
    @State private var scale: CGFloat = 0.5
    @State private var opacity: Double = 0.0
    
    var body: some View {
        ZStack {
            LinearGradient(
                gradient: Gradient(colors: [Color.blue.opacity(0.8), Color.purple.opacity(0.8)]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: 20) {
                Image(systemName: "wand.and.stars")
                    .font(.system(size: 80))
                    .foregroundColor(.white)
                    .scaleEffect(scale)
                    .opacity(opacity)
                
                Text("Lmini")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .opacity(opacity)
                
                Text("AI Photo Enhancement")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                    .opacity(opacity)
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 1.0)) {
                scale = 1.0
                opacity = 1.0
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                onComplete()
            }
        }
    }
}

// MARK: - Onboarding View

struct OnboardingView: View {
    let onComplete: () -> Void
    @State private var currentPage = 0
    
    private let pages = [
        OnboardingPage(
            title: "AI Photo Enhancement",
            description: "Transform your photos with powerful AI technology",
            imageName: "wand.and.stars",
            color: .blue
        ),
        OnboardingPage(
            title: "Multiple Filters",
            description: "Choose from various AI filters including anime style, beauty mode, and more",
            imageName: "camera.filters",
            color: .purple
        ),
        OnboardingPage(
            title: "Batch Processing",
            description: "Process multiple images at once to save time",
            imageName: "photo.stack",
            color: .green
        ),
        OnboardingPage(
            title: "Get Started",
            description: "Ready to enhance your photos with AI?",
            imageName: "checkmark.circle",
            color: .orange
        )
    ]
    
    var body: some View {
        VStack {
            TabView(selection: $currentPage) {
                ForEach(0..<pages.count, id: \.self) { index in
                    OnboardingPageView(page: pages[index])
                        .tag(index)
                }
            }
            .tabViewStyle(PageTabViewStyle())
            .indexViewStyle(PageIndexViewStyle(backgroundDisplayMode: .always))
            
            Button(action: {
                if currentPage < pages.count - 1 {
                    withAnimation {
                        currentPage += 1
                    }
                } else {
                    onComplete()
                }
            }) {
                Text(currentPage < pages.count - 1 ? "Next" : "Get Started")
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(pages[currentPage].color)
                    .clipShape(RoundedRectangle(cornerRadius: 16))
            }
            .padding(.horizontal, 20)
            .padding(.bottom, 30)
        }
    }
}

struct OnboardingPage {
    let title: String
    let description: String
    let imageName: String
    let color: Color
}

struct OnboardingPageView: View {
    let page: OnboardingPage
    
    var body: some View {
        VStack(spacing: 30) {
            Spacer()
            
            Image(systemName: page.imageName)
                .font(.system(size: 100))
                .foregroundColor(page.color)
            
            VStack(spacing: 16) {
                Text(page.title)
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                
                Text(page.description)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
            }
            
            Spacer()
        }
    }
}

// MARK: - Preview

struct ContentView_Previews: PreviewProvider {
    static var previews: some View {
        ContentView()
            .environmentObject(AppConfiguration.shared)
            .environmentObject(HistoryManager.shared)
            .environmentObject(ErrorHandler.shared)
            .environmentObject(NotificationManager.shared)
            .environmentObject(LoadingStateManager.shared)
    }
}
