//
//  FilterSelectionCard.swift
//  Lmini
//
//  Created by <PERSON>B<PERSON> Pro-Leo on 5/30/25.
//

import SwiftUI

struct FilterSelectionCard: View {
    let selectedFilter: AIImageService.FilterType
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: selectedFilter.icon)
                    .font(.system(size: 20))
                    .foregroundColor(.blue)
                    .frame(width: 30)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(selectedFilter.displayName)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(selectedFilter.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(16)
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
        .padding(.horizontal, 20)
    }
}

#Preview {
    FilterSelectionCard(selectedFilter: .enhance) {
        // Preview action
    }
}
