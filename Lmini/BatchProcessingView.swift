//
//  BatchProcessingView.swift
//  Lmini
//
//  Created by <PERSON><PERSON><PERSON> Pro-Leo on 5/30/25.
//

import SwiftUI
import PhotosUI

struct BatchProcessingView: View {
    @State private var selectedImages: [UIImage] = []
    @State private var processedImages: [UIImage] = []
    @State private var selectedFilter: AIImageService.FilterType = .enhance
    @State private var isProcessing = false
    @State private var currentProcessingIndex = 0
    @State private var currentProgress: Double = 0.0
    @State private var showImagePicker = false
    @State private var showShareSheet = false
    @State private var errorMessage: String?
    @State private var showError = false
    
    private let aiService = AIImageService.shared
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 进度指示器
                if isProcessing {
                    progressIndicator
                }
                
                // 图片网格
                imageGrid
                
                // 滤镜选择
                filterSelection
                
                // 控制按钮
                controlButtons
            }
            .navigationTitle("Batch Processing")
            .navigationBarTitleDisplayMode(.inline)
            .sheet(isPresented: $showImagePicker) {
                MultiImagePicker(images: $selectedImages)
            }
            .sheet(isPresented: $showShareSheet) {
                ShareSheet(activityItems: processedImages)
            }
            .alert("Error", isPresented: $showError) {
                Button("OK") { }
            } message: {
                Text(errorMessage ?? "Unknown error occurred")
            }
        }
    }
    
    // MARK: - Progress Indicator
    
    private var progressIndicator: some View {
        VStack(spacing: 8) {
            HStack {
                Text("Processing image \(currentProcessingIndex + 1) of \(selectedImages.count)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
                Text("\(Int(currentProgress * 100))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            ProgressView(value: currentProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: .blue))
            
            ProgressView(value: Double(currentProcessingIndex), total: Double(selectedImages.count))
                .progressViewStyle(LinearProgressViewStyle(tint: .green))
        }
        .padding()
        .background(Color.gray.opacity(0.1))
    }
    
    // MARK: - Image Grid
    
    private var imageGrid: some View {
        ScrollView {
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 2), spacing: 8) {
                ForEach(0..<max(selectedImages.count, processedImages.count), id: \.self) { index in
                    ImageComparisonCard(
                        originalImage: index < selectedImages.count ? selectedImages[index] : nil,
                        processedImage: index < processedImages.count ? processedImages[index] : nil,
                        isProcessing: isProcessing && index == currentProcessingIndex,
                        progress: isProcessing && index == currentProcessingIndex ? currentProgress : 0.0
                    )
                }
                
                // 添加图片卡片
                if !isProcessing {
                    AddImageCard {
                        showImagePicker = true
                    }
                }
            }
            .padding()
        }
    }
    
    // MARK: - Filter Selection
    
    private var filterSelection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Select Filter")
                .font(.headline)
                .padding(.horizontal)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(AIImageService.FilterType.allCases, id: \.self) { filter in
                        FilterChip(
                            filter: filter,
                            isSelected: selectedFilter == filter
                        ) {
                            selectedFilter = filter
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding(.vertical)
        .background(Color.gray.opacity(0.05))
    }
    
    // MARK: - Control Buttons
    
    private var controlButtons: some View {
        VStack(spacing: 12) {
            HStack(spacing: 16) {
                Button(action: {
                    showImagePicker = true
                }) {
                    HStack {
                        Image(systemName: "photo.stack")
                        Text("Add Images")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .foregroundColor(.blue)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                }
                
                Button(action: {
                    processAllImages()
                }) {
                    HStack {
                        Image(systemName: isProcessing ? "stop.circle" : "wand.and.stars")
                        Text(isProcessing ? "Stop" : "Process All")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(selectedImages.isEmpty ? Color.gray.opacity(0.1) : Color.green.opacity(0.1))
                    .foregroundColor(selectedImages.isEmpty ? .gray : .green)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                }
                .disabled(selectedImages.isEmpty)
            }
            
            if !processedImages.isEmpty {
                HStack(spacing: 16) {
                    Button(action: {
                        clearAll()
                    }) {
                        HStack {
                            Image(systemName: "trash")
                            Text("Clear All")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.red.opacity(0.1))
                        .foregroundColor(.red)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                    }
                    
                    Button(action: {
                        showShareSheet = true
                    }) {
                        HStack {
                            Image(systemName: "square.and.arrow.up")
                            Text("Share All")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.purple.opacity(0.1))
                        .foregroundColor(.purple)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                    }
                }
            }
        }
        .padding()
    }
    
    // MARK: - Helper Methods
    
    private func processAllImages() {
        guard !selectedImages.isEmpty, !isProcessing else { return }
        
        isProcessing = true
        currentProcessingIndex = 0
        currentProgress = 0.0
        processedImages = []
        
        aiService.enhanceImages(
            selectedImages,
            filterType: selectedFilter,
            provider: .mock,
            progressCallback: { index, progress in
                DispatchQueue.main.async {
                    self.currentProcessingIndex = index
                    self.currentProgress = progress
                }
            },
            completion: { result in
                DispatchQueue.main.async {
                    self.isProcessing = false
                    
                    switch result {
                    case .success(let enhancedImages):
                        self.processedImages = enhancedImages
                    case .failure(let error):
                        self.errorMessage = error.localizedDescription
                        self.showError = true
                    }
                }
            }
        )
    }
    
    private func clearAll() {
        selectedImages = []
        processedImages = []
        currentProcessingIndex = 0
        currentProgress = 0.0
    }
}

// MARK: - Image Comparison Card

struct ImageComparisonCard: View {
    let originalImage: UIImage?
    let processedImage: UIImage?
    let isProcessing: Bool
    let progress: Double
    
    var body: some View {
        VStack(spacing: 8) {
            HStack(spacing: 4) {
                // 原图
                if let originalImage = originalImage {
                    VStack {
                        Text("Before")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        
                        Image(uiImage: originalImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 70, height: 70)
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                    }
                }
                
                // 处理后的图
                VStack {
                    Text("After")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    ZStack {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.gray.opacity(0.2))
                            .frame(width: 70, height: 70)
                        
                        if isProcessing {
                            VStack {
                                ProgressView()
                                    .scaleEffect(0.8)
                                Text("\(Int(progress * 100))%")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        } else if let processedImage = processedImage {
                            Image(uiImage: processedImage)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: 70, height: 70)
                                .clipShape(RoundedRectangle(cornerRadius: 8))
                        } else {
                            Image(systemName: "photo")
                                .foregroundColor(.gray)
                        }
                    }
                }
            }
        }
        .padding(8)
        .background(Color.white)
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// MARK: - Add Image Card

struct AddImageCard: View {
    let action: () -> Void
    
    var body: some View {
        VStack {
            Image(systemName: "plus.circle.dashed")
                .font(.title)
                .foregroundColor(.blue)
            Text("Add Images")
                .font(.caption)
                .foregroundColor(.blue)
        }
        .frame(height: 120)
        .frame(maxWidth: .infinity)
        .background(Color.blue.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .onTapGesture {
            action()
        }
    }
}

// MARK: - Filter Chip

struct FilterChip: View {
    let filter: AIImageService.FilterType
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        HStack(spacing: 6) {
            Image(systemName: filter.icon)
                .font(.caption)
            Text(filter.displayName)
                .font(.caption)
                .fontWeight(.medium)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(isSelected ? Color.blue : Color.gray.opacity(0.2))
        .foregroundColor(isSelected ? .white : .primary)
        .clipShape(Capsule())
        .onTapGesture {
            action()
        }
    }
}

// MARK: - Preview

struct BatchProcessingView_Previews: PreviewProvider {
    static var previews: some View {
        BatchProcessingView()
    }
}
