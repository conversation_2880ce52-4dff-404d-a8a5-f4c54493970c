//
//  FilterSelectionView.swift
//  Lmini
//
//  Created by Mac<PERSON>ook Pro-Leo on 5/30/25.
//

import SwiftUI

struct FilterSelectionView: View {
    @State private var selectedFilter: AIImageService.FilterType = .enhance
    @State private var isProcessing = false
    @State private var processingProgress: Double = 0.0
    @State private var originalImage: UIImage?
    @State private var processedImage: UIImage?
    @State private var showImagePicker = false
    @State private var showShareSheet = false
    @State private var errorMessage: String?
    @State private var showError = false
    
    private let aiService = AIImageService.shared
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 图像显示区域
                imageDisplayArea
                
                // 滤镜选择区域
                filterSelectionArea
                
                // 控制按钮区域
                controlButtonsArea
            }
            .navigationTitle("AI Filters")
            .navigationBarTitleDisplayMode(.inline)
            .sheet(isPresented: $showImagePicker) {
                ImagePicker(image: $originalImage)
            }
            .sheet(isPresented: $showShareSheet) {
                if let image = processedImage {
                    ShareSheet(activityItems: [image])
                }
            }
            .alert("Error", isPresented: $showError) {
                Button("OK") { }
            } message: {
                Text(errorMessage ?? "Unknown error occurred")
            }
        }
    }
    
    // MARK: - Image Display Area
    
    private var imageDisplayArea: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.gray.opacity(0.1))
                .frame(height: 300)
            
            if let originalImage = originalImage {
                HStack(spacing: 16) {
                    // 原图
                    VStack {
                        Text("Before")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Image(uiImage: originalImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(maxWidth: 140, maxHeight: 200)
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                    }
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 1, height: 150)
                    
                    // 处理后的图
                    VStack {
                        Text("After")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        ZStack {
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.gray.opacity(0.2))
                                .frame(maxWidth: 140, maxHeight: 200)
                            
                            if isProcessing {
                                VStack {
                                    ProgressView()
                                        .scaleEffect(1.2)
                                    Text("\(Int(processingProgress * 100))%")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                        .padding(.top, 8)
                                }
                            } else if let processedImage = processedImage {
                                Image(uiImage: processedImage)
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(maxWidth: 140, maxHeight: 200)
                                    .clipShape(RoundedRectangle(cornerRadius: 12))
                            } else {
                                VStack {
                                    Image(systemName: "wand.and.stars")
                                        .font(.title2)
                                        .foregroundColor(.gray)
                                    Text("Tap Process")
                                        .font(.caption)
                                        .foregroundColor(.gray)
                                }
                            }
                        }
                    }
                }
            } else {
                VStack {
                    Image(systemName: "photo.badge.plus")
                        .font(.system(size: 50))
                        .foregroundColor(.gray)
                    Text("Tap to select an image")
                        .font(.headline)
                        .foregroundColor(.gray)
                        .padding(.top, 8)
                }
                .onTapGesture {
                    showImagePicker = true
                }
            }
        }
        .padding(.horizontal)
    }
    
    // MARK: - Filter Selection Area
    
    private var filterSelectionArea: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Choose Filter")
                .font(.headline)
                .padding(.horizontal)
            
            ScrollView(.horizontal, showsIndicators: false) {
                LazyHStack(spacing: 12) {
                    ForEach(AIImageService.FilterType.allCases, id: \.self) { filter in
                        FilterCard(
                            filter: filter,
                            isSelected: selectedFilter == filter
                        ) {
                            selectedFilter = filter
                            if originalImage != nil && !isProcessing {
                                processImage()
                            }
                        }
                    }
                }
                .padding(.horizontal)
            }
            
            // 选中滤镜的描述
            VStack(alignment: .leading, spacing: 4) {
                Text(selectedFilter.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                Text(selectedFilter.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal)
        }
        .padding(.vertical)
    }
    
    // MARK: - Control Buttons Area
    
    private var controlButtonsArea: some View {
        VStack(spacing: 12) {
            HStack(spacing: 16) {
                // 选择图片按钮
                Button(action: {
                    showImagePicker = true
                }) {
                    HStack {
                        Image(systemName: "photo.on.rectangle")
                        Text("Select Image")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .foregroundColor(.blue)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                }
                
                // 处理图片按钮
                Button(action: {
                    processImage()
                }) {
                    HStack {
                        Image(systemName: isProcessing ? "stop.circle" : "wand.and.stars")
                        Text(isProcessing ? "Processing..." : "Process")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(originalImage != nil ? Color.green.opacity(0.1) : Color.gray.opacity(0.1))
                    .foregroundColor(originalImage != nil ? .green : .gray)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                }
                .disabled(originalImage == nil || isProcessing)
            }
            
            // 分享按钮
            if processedImage != nil {
                Button(action: {
                    showShareSheet = true
                }) {
                    HStack {
                        Image(systemName: "square.and.arrow.up")
                        Text("Share Result")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.purple.opacity(0.1))
                    .foregroundColor(.purple)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                }
            }
        }
        .padding()
    }
    
    // MARK: - Helper Methods
    
    private func processImage() {
        guard let image = originalImage, !isProcessing else { return }
        
        isProcessing = true
        processingProgress = 0.0
        processedImage = nil
        
        aiService.enhanceImage(
            image,
            filterType: selectedFilter,
            provider: .mock,
            progressCallback: { progress in
                DispatchQueue.main.async {
                    self.processingProgress = progress
                }
            },
            completion: { result in
                DispatchQueue.main.async {
                    self.isProcessing = false
                    
                    switch result {
                    case .success(let enhancedImage):
                        self.processedImage = enhancedImage
                    case .failure(let error):
                        self.errorMessage = error.localizedDescription
                        self.showError = true
                    }
                }
            }
        )
    }
}

// MARK: - Filter Card Component

struct FilterCard: View {
    let filter: AIImageService.FilterType
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        VStack(spacing: 8) {
            ZStack {
                Circle()
                    .fill(isSelected ? Color.blue : Color.gray.opacity(0.2))
                    .frame(width: 60, height: 60)
                
                Image(systemName: filter.icon)
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : .gray)
            }
            
            Text(filter.displayName)
                .font(.caption)
                .fontWeight(isSelected ? .medium : .regular)
                .foregroundColor(isSelected ? .blue : .primary)
                .multilineTextAlignment(.center)
                .frame(width: 80)
        }
        .onTapGesture {
            action()
        }
    }
}

// MARK: - Preview

struct FilterSelectionView_Previews: PreviewProvider {
    static var previews: some View {
        FilterSelectionView()
    }
}
