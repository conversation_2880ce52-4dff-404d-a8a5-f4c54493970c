//
//  FeatureDetailView.swift
//  Lmini
//
//  Created by <PERSON><PERSON><PERSON> Pro-Leo on 5/30/25.
//

import SwiftUI

struct FeatureDetailView: View {
    let feature: FeatureType
    let onDismiss: () -> Void
    
    @State private var selectedImage: UIImage?
    @State private var processedImage: UIImage?
    @State private var isProcessing = false
    @State private var processingProgress: Double = 0.0
    @State private var showImagePicker = false
    @State private var showShareSheet = false
    @State private var errorMessage: String?
    @State private var showError = false
    
    private let aiService = AIImageService.shared
    
    var body: some View {
        ZStack {
            // 背景渐变
            backgroundGradient
            
            VStack(spacing: 0) {
                // 顶部导航栏
                topNavigationBar
                
                // 主要内容
                mainContent
                
                // 底部控制区域
                bottomControls
            }
        }
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(image: $selectedImage)
        }
        .sheet(isPresented: $showShareSheet) {
            if let image = processedImage {
                ShareSheet(activityItems: [image])
            }
        }
        .alert("Error", isPresented: $showError) {
            Button("OK") { }
        } message: {
            Text(errorMessage ?? "Unknown error occurred")
        }
        .onChange(of: selectedImage) { _ in
            if selectedImage != nil {
                processImage()
            }
        }
    }
    
    // MARK: - Background
    
    private var backgroundGradient: some View {
        LinearGradient(
            gradient: Gradient(colors: [
                featureBackgroundColor.opacity(0.3),
                featureBackgroundColor.opacity(0.1),
                Color.white
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    private var featureBackgroundColor: Color {
        switch feature {
        case .enhance: return .pink
        case .anime: return .teal
        case .future: return .orange
        case .beauty: return .blue
        }
    }
    
    // MARK: - Top Navigation Bar
    
    private var topNavigationBar: some View {
        HStack {
            Button(action: onDismiss) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .foregroundColor(.black)
                    .frame(width: 32, height: 32)
                    .background(Circle().fill(Color.white.opacity(0.8)))
            }
            
            Spacer()
            
            if processedImage != nil {
                Button(action: {
                    showShareSheet = true
                }) {
                    Image(systemName: "square.and.arrow.up")
                        .font(.title2)
                        .foregroundColor(.black)
                        .frame(width: 32, height: 32)
                        .background(Circle().fill(Color.white.opacity(0.8)))
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 10)
    }
    
    // MARK: - Main Content
    
    private var mainContent: some View {
        VStack(spacing: 20) {
            // 标题
            VStack(spacing: 8) {
                Text(feature.title)
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.black)
                    .multilineTextAlignment(.center)
                
                Text(feature.description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding(.horizontal, 20)
            .padding(.top, 20)
            
            Spacer()
            
            // 图像处理区域
            imageProcessingArea
            
            Spacer()
        }
    }
    
    // MARK: - Image Processing Area
    
    private var imageProcessingArea: some View {
        ZStack {
            // 手机框架
            RoundedRectangle(cornerRadius: 25)
                .fill(Color.black)
                .frame(width: 280, height: 500)
            
            VStack(spacing: 0) {
                // 状态栏
                statusBar
                
                // 主要图像区域
                mainImageArea
                
                // 底部控制栏
                phoneBottomControls
            }
            .frame(width: 260, height: 480)
        }
    }
    
    private var statusBar: some View {
        HStack {
            Circle()
                .fill(Color.white)
                .frame(width: 6, height: 6)
            
            Spacer()
            
            HStack(spacing: 3) {
                Rectangle()
                    .fill(Color.white)
                    .frame(width: 15, height: 8)
                    .clipShape(RoundedRectangle(cornerRadius: 2))
                
                Rectangle()
                    .fill(Color.white)
                    .frame(width: 2, height: 6)
                    .clipShape(RoundedRectangle(cornerRadius: 1))
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 15)
    }
    
    private var mainImageArea: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.gray.opacity(0.2))
                .frame(height: 380)
            
            if let selectedImage = selectedImage {
                if feature == .enhance {
                    // 增强功能显示前后对比
                    enhanceComparisonView(originalImage: selectedImage)
                } else {
                    // 其他功能显示单一处理结果
                    singleImageProcessingView(originalImage: selectedImage)
                }
            } else {
                // 空状态
                VStack(spacing: 16) {
                    Image(systemName: "photo.badge.plus")
                        .font(.system(size: 50))
                        .foregroundColor(.white.opacity(0.7))
                    
                    Text("Tap to select photo")
                        .font(.headline)
                        .foregroundColor(.white)
                }
                .onTapGesture {
                    showImagePicker = true
                }
            }
        }
        .padding(.horizontal, 15)
        .padding(.vertical, 20)
    }
    
    private func enhanceComparisonView(originalImage: UIImage) -> some View {
        HStack(spacing: 8) {
            // Before
            VStack(spacing: 8) {
                Text("Before")
                    .font(.caption)
                    .foregroundColor(.white)
                
                Image(uiImage: originalImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 110, height: 140)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
            }
            
            // 分隔线
            Rectangle()
                .fill(Color.white.opacity(0.3))
                .frame(width: 1, height: 120)
            
            // After
            VStack(spacing: 8) {
                Text("After")
                    .font(.caption)
                    .foregroundColor(.white)
                
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 110, height: 140)
                    
                    if isProcessing {
                        VStack {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(1.2)
                            
                            Text("\(Int(processingProgress * 100))%")
                                .font(.caption)
                                .foregroundColor(.white)
                                .padding(.top, 8)
                        }
                    } else if let processedImage = processedImage {
                        Image(uiImage: processedImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 110, height: 140)
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                    } else {
                        VStack {
                            Image(systemName: "wand.and.stars")
                                .font(.title2)
                                .foregroundColor(.white.opacity(0.7))
                            Text("Processing...")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.7))
                        }
                    }
                }
            }
        }
    }
    
    private func singleImageProcessingView(originalImage: UIImage) -> some View {
        VStack(spacing: 16) {
            // 功能特定的标题
            Text(featureProcessingTitle)
                .font(.headline)
                .foregroundColor(.white)
            
            ZStack {
                if isProcessing {
                    VStack {
                        Image(uiImage: originalImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 200, height: 250)
                            .clipShape(RoundedRectangle(cornerRadius: 16))
                            .overlay(
                                ZStack {
                                    Color.black.opacity(0.5)
                                    VStack {
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                            .scaleEffect(1.5)
                                        
                                        Text("Processing...")
                                            .font(.subheadline)
                                            .foregroundColor(.white)
                                            .padding(.top, 12)
                                        
                                        Text("\(Int(processingProgress * 100))%")
                                            .font(.caption)
                                            .foregroundColor(.white.opacity(0.8))
                                    }
                                }
                                .clipShape(RoundedRectangle(cornerRadius: 16))
                            )
                    }
                } else if let processedImage = processedImage {
                    Image(uiImage: processedImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 200, height: 250)
                        .clipShape(RoundedRectangle(cornerRadius: 16))
                } else {
                    Image(uiImage: originalImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 200, height: 250)
                        .clipShape(RoundedRectangle(cornerRadius: 16))
                }
            }
        }
    }
    
    private var featureProcessingTitle: String {
        switch feature {
        case .enhance: return "Enhancing Quality..."
        case .anime: return "Creating Anime Style..."
        case .future: return "Predicting Future..."
        case .beauty: return "Applying Beauty Mode..."
        }
    }
    
    private var phoneBottomControls: some View {
        HStack {
            // 功能按钮
            ForEach(0..<5) { index in
                Circle()
                    .fill(Color.white.opacity(index == 2 ? 1.0 : 0.4))
                    .frame(width: index == 2 ? 12 : 8, height: index == 2 ? 12 : 8)
            }
        }
        .padding(.bottom, 20)
    }
    
    // MARK: - Bottom Controls
    
    private var bottomControls: some View {
        VStack(spacing: 16) {
            if selectedImage == nil {
                Button(action: {
                    showImagePicker = true
                }) {
                    HStack {
                        Image(systemName: "photo.on.rectangle")
                        Text("Select Photo")
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(featureBackgroundColor)
                    .clipShape(RoundedRectangle(cornerRadius: 16))
                }
                .padding(.horizontal, 20)
            } else {
                HStack(spacing: 16) {
                    Button(action: {
                        selectedImage = nil
                        processedImage = nil
                        isProcessing = false
                        processingProgress = 0.0
                    }) {
                        HStack {
                            Image(systemName: "arrow.clockwise")
                            Text("Try Again")
                        }
                        .font(.subheadline)
                        .foregroundColor(featureBackgroundColor)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.white)
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(featureBackgroundColor, lineWidth: 2)
                        )
                        .clipShape(RoundedRectangle(cornerRadius: 16))
                    }
                    
                    if processedImage != nil {
                        Button(action: {
                            showShareSheet = true
                        }) {
                            HStack {
                                Image(systemName: "square.and.arrow.up")
                                Text("Share")
                            }
                            .font(.subheadline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(featureBackgroundColor)
                            .clipShape(RoundedRectangle(cornerRadius: 16))
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
        }
        .padding(.bottom, 30)
    }
    
    // MARK: - Helper Methods
    
    private func processImage() {
        guard let image = selectedImage, !isProcessing else { return }
        
        isProcessing = true
        processingProgress = 0.0
        processedImage = nil
        
        aiService.enhanceImage(
            image,
            filterType: feature.filterType,
            provider: .mock,
            progressCallback: { progress in
                DispatchQueue.main.async {
                    self.processingProgress = progress
                }
            },
            completion: { result in
                DispatchQueue.main.async {
                    self.isProcessing = false
                    
                    switch result {
                    case .success(let enhancedImage):
                        self.processedImage = enhancedImage
                        // 保存到历史记录
                        HistoryManager.shared.addHistoryItem(
                            originalImage: image,
                            processedImage: enhancedImage,
                            filterType: self.feature.filterType
                        )
                    case .failure(let error):
                        self.errorMessage = error.localizedDescription
                        self.showError = true
                    }
                }
            }
        )
    }
}

// MARK: - Preview

struct FeatureDetailView_Previews: PreviewProvider {
    static var previews: some View {
        FeatureDetailView(feature: .enhance) { }
    }
}
