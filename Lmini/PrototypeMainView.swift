//
//  PrototypeMainView.swift
//  Lmini
//
//  Created by Mac<PERSON>ook Pro-Leo on 5/30/25.
//

import SwiftUI

struct PrototypeMainView: View {
    @State private var selectedFeature: FeatureType?
    @State private var animateCards = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Enhanced Header
                    headerSection

                    // Enhanced Feature Cards Grid
                    featureCardsGrid

                    // Additional Features Section
                    additionalFeaturesSection

                    Spacer(minLength: 100)
                }
            }
            .navigationBarHidden(true)
            .background(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color(.systemBackground),
                        Color(.systemGray6).opacity(0.3)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
        }
        .fullScreenCover(item: $selectedFeature) { feature in
            FeatureDetailView(feature: feature) {
                selectedFeature = nil
            }
        }
        .onAppear {
            withAnimation(.easeInOut(duration: 0.8).delay(0.2)) {
                animateCards = true
            }
        }
    }

    // MARK: - Header Section

    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Lmini")
                        .font(.system(size: 32, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)

                    Text("AI Photo Enhancement")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text("Transform your photos with cutting-edge AI")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                // Profile/Settings Button
                Button(action: {}) {
                    Image(systemName: "person.crop.circle.fill")
                        .font(.title)
                        .foregroundColor(.blue)
                }
            }

            // Stats Row
            HStack(spacing: 20) {
                StatItem(title: "Photos Enhanced", value: "1.2M+")
                StatItem(title: "Happy Users", value: "50K+")
                StatItem(title: "AI Models", value: "15+")
            }
        }
        .padding(.horizontal, 20)
        .padding(.top, 20)
    }

    // MARK: - Feature Cards Grid

    private var featureCardsGrid: some View {
        LazyVGrid(columns: [
            GridItem(.flexible(), spacing: 16),
            GridItem(.flexible(), spacing: 16)
        ], spacing: 16) {
            EnhancedFeatureCard(
                feature: .enhance,
                title: "Enhance your photos' quality.",
                backgroundColor: Color.pink.opacity(0.15),
                accentColor: Color.pink,
                isAnimated: animateCards
            ) {
                selectedFeature = .enhance
            }

            EnhancedFeatureCard(
                feature: .anime,
                title: "Your 90s anime version.",
                backgroundColor: Color.teal.opacity(0.15),
                accentColor: Color.teal,
                isAnimated: animateCards
            ) {
                selectedFeature = .anime
            }

            EnhancedFeatureCard(
                feature: .future,
                title: "See your future family.",
                backgroundColor: Color.orange.opacity(0.15),
                accentColor: Color.orange,
                isAnimated: animateCards
            ) {
                selectedFeature = .future
            }

            EnhancedFeatureCard(
                feature: .beauty,
                title: "Get the best face in every photo.",
                backgroundColor: Color.blue.opacity(0.15),
                accentColor: Color.blue,
                isAnimated: animateCards
            ) {
                selectedFeature = .beauty
            }
        }
        .padding(.horizontal, 20)
    }

    // MARK: - Additional Features Section

    private var additionalFeaturesSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("More Features")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Spacer()

                Button("See All") {
                    // Navigate to all features
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
            .padding(.horizontal, 20)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(additionalFeatures, id: \.title) { feature in
                        AdditionalFeatureCard(feature: feature)
                    }
                }
                .padding(.horizontal, 20)
            }
        }
    }

    private var additionalFeatures: [AdditionalFeature] {
        [
            AdditionalFeature(title: "Batch Process", icon: "photo.stack", color: .purple),
            AdditionalFeature(title: "History", icon: "clock.arrow.circlepath", color: .green),
            AdditionalFeature(title: "Share", icon: "square.and.arrow.up", color: .indigo),
            AdditionalFeature(title: "Settings", icon: "gear", color: .gray)
        ]
    }
}

// MARK: - Feature Types

enum FeatureType: String, CaseIterable, Identifiable {
    case enhance = "enhance"
    case anime = "anime"
    case future = "future"
    case beauty = "beauty"

    var id: String { rawValue }

    var title: String {
        switch self {
        case .enhance: return "Photo Enhancement"
        case .anime: return "Anime Style"
        case .future: return "Future Family"
        case .beauty: return "Beauty Mode"
        }
    }

    var description: String {
        switch self {
        case .enhance: return "Enhance your photos' quality with AI technology"
        case .anime: return "Transform into your 90s anime version"
        case .future: return "See what your future family might look like"
        case .beauty: return "Get the best face in every photo"
        }
    }

    var filterType: AIImageService.FilterType {
        switch self {
        case .enhance: return .enhance
        case .anime: return .anime
        case .future: return .future
        case .beauty: return .beauty
        }
    }
}

// MARK: - Supporting Views

struct StatItem: View {
    let title: String
    let value: String

    var body: some View {
        VStack(alignment: .leading, spacing: 2) {
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

struct AdditionalFeature {
    let title: String
    let icon: String
    let color: Color
}

struct AdditionalFeatureCard: View {
    let feature: AdditionalFeature

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: feature.icon)
                .font(.title2)
                .foregroundColor(feature.color)
                .frame(width: 40, height: 40)
                .background(feature.color.opacity(0.1))
                .clipShape(Circle())

            Text(feature.title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.primary)
        }
        .frame(width: 80, height: 80)
        .background(Color(.systemGray6).opacity(0.5))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
}

// MARK: - Enhanced Feature Card

struct EnhancedFeatureCard: View {
    let feature: FeatureType
    let title: String
    let backgroundColor: Color
    let accentColor: Color
    let isAnimated: Bool
    let action: () -> Void

    @State private var isPressed = false

    var body: some View {
        VStack(spacing: 0) {
            // Enhanced Title Area
            VStack(alignment: .leading, spacing: 12) {
                Text(title)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                    .multilineTextAlignment(.leading)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .lineLimit(2)

                Spacer()
            }
            .padding(.top, 20)
            .padding(.horizontal, 16)
            .frame(height: 80)

            // Enhanced Phone Mockup
            ZStack {
                // Phone Frame with Gradient
                RoundedRectangle(cornerRadius: 20)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.black,
                                Color.black.opacity(0.8)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(height: 220)
                    .shadow(color: accentColor.opacity(0.3), radius: 8, x: 0, y: 4)

                // Enhanced Phone Interface
                VStack(spacing: 10) {
                    // Realistic Status Bar
                    enhancedStatusBar

                    // Main Content Area with Feature-Specific Design
                    ZStack {
                        RoundedRectangle(cornerRadius: 16)
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.gray.opacity(0.2),
                                        Color.gray.opacity(0.1)
                                    ]),
                                    startPoint: .top,
                                    endPoint: .bottom
                                )
                            )
                            .frame(height: 150)

                        // Enhanced Feature Preview
                        enhancedFeaturePreviewContent
                    }
                    .padding(.horizontal, 10)

                    // Enhanced Bottom Controls
                    enhancedBottomControls
                }

                // Floating Action Button
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        Button(action: action) {
                            Image(systemName: "arrow.down.circle.fill")
                                .font(.system(size: 24))
                                .foregroundColor(.white)
                                .background(
                                    Circle()
                                        .fill(accentColor)
                                        .frame(width: 36, height: 36)
                                )
                                .shadow(color: accentColor.opacity(0.5), radius: 4, x: 0, y: 2)
                        }
                        .scaleEffect(isPressed ? 0.9 : 1.0)
                        .animation(.easeInOut(duration: 0.1), value: isPressed)
                        .padding(.trailing, 16)
                        .padding(.bottom, 16)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(backgroundColor)
                .shadow(color: accentColor.opacity(0.2), radius: 12, x: 0, y: 6)
        )
        .scaleEffect(isAnimated ? 1.0 : 0.8)
        .opacity(isAnimated ? 1.0 : 0.0)
        .animation(.spring(response: 0.6, dampingFraction: 0.8).delay(Double(feature.rawValue.hashValue % 4) * 0.1), value: isAnimated)
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onTapGesture {
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
                action()
            }
        }
    }

    // MARK: - Enhanced Phone Components

    private var enhancedStatusBar: some View {
        HStack {
            // Time
            Text("9:41")
                .font(.system(size: 12, weight: .semibold))
                .foregroundColor(.white)

            Spacer()

            // Signal and Battery
            HStack(spacing: 4) {
                // Signal bars
                HStack(spacing: 2) {
                    ForEach(0..<4) { index in
                        RoundedRectangle(cornerRadius: 1)
                            .fill(Color.white)
                            .frame(width: 3, height: CGFloat(4 + index * 2))
                    }
                }

                // Battery
                ZStack {
                    RoundedRectangle(cornerRadius: 2)
                        .stroke(Color.white, lineWidth: 1)
                        .frame(width: 20, height: 10)

                    RoundedRectangle(cornerRadius: 1)
                        .fill(Color.white)
                        .frame(width: 16, height: 6)

                    // Battery tip
                    RoundedRectangle(cornerRadius: 1)
                        .fill(Color.white)
                        .frame(width: 1, height: 4)
                        .offset(x: 12)
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.top, 12)
    }

    private var enhancedBottomControls: some View {
        HStack(spacing: 8) {
            ForEach(0..<5) { index in
                Circle()
                    .fill(Color.white.opacity(index == 2 ? 1.0 : 0.4))
                    .frame(width: index == 2 ? 10 : 6, height: index == 2 ? 10 : 6)
                    .scaleEffect(index == 2 ? 1.2 : 1.0)
                    .animation(.easeInOut(duration: 0.3), value: index == 2)
            }
        }
        .padding(.bottom, 12)
    }

    @ViewBuilder
    private var enhancedFeaturePreviewContent: some View {
        switch feature {
        case .enhance:
            HStack(spacing: 12) {
                // Before Image
                VStack(spacing: 6) {
                    Text("Before")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))

                    RoundedRectangle(cornerRadius: 10)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.gray.opacity(0.6),
                                    Color.gray.opacity(0.4)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 55, height: 70)
                        .overlay(
                            VStack {
                                Image(systemName: "photo")
                                    .foregroundColor(.white.opacity(0.7))
                                    .font(.system(size: 16))
                                Text("Low Quality")
                                    .font(.system(size: 8))
                                    .foregroundColor(.white.opacity(0.6))
                            }
                        )
                        .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
                }

                // Arrow
                Image(systemName: "arrow.right")
                    .font(.system(size: 12, weight: .bold))
                    .foregroundColor(accentColor)

                // After Image
                VStack(spacing: 6) {
                    Text("After")
                        .font(.system(size: 10, weight: .medium))
                        .foregroundColor(.white.opacity(0.8))

                    RoundedRectangle(cornerRadius: 10)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    accentColor.opacity(0.8),
                                    accentColor.opacity(0.6)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 55, height: 70)
                        .overlay(
                            VStack {
                                Image(systemName: "sparkles")
                                    .foregroundColor(.white)
                                    .font(.system(size: 16))
                                Text("Enhanced")
                                    .font(.system(size: 8))
                                    .foregroundColor(.white.opacity(0.9))
                            }
                        )
                        .shadow(color: accentColor.opacity(0.4), radius: 4, x: 0, y: 2)
                }
            }

        case .anime:
            VStack(spacing: 8) {
                Text("90s Anime Style")
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(.white.opacity(0.9))

                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    accentColor.opacity(0.8),
                                    accentColor.opacity(0.6)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 90, height: 90)
                        .shadow(color: accentColor.opacity(0.4), radius: 4, x: 0, y: 2)

                    VStack {
                        Image(systemName: "person.crop.circle.fill")
                            .foregroundColor(.white)
                            .font(.system(size: 24))
                        Text("Anime")
                            .font(.system(size: 10, weight: .bold))
                            .foregroundColor(.white)
                    }
                }
            }

        case .future:
            VStack(spacing: 8) {
                Text("Future Family")
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(.white.opacity(0.9))

                HStack(spacing: 6) {
                    // Parent 1
                    Circle()
                        .fill(Color.white.opacity(0.8))
                        .frame(width: 28, height: 28)
                        .overlay(
                            Image(systemName: "person.fill")
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                        )

                    // Parent 2
                    Circle()
                        .fill(Color.white.opacity(0.8))
                        .frame(width: 28, height: 28)
                        .overlay(
                            Image(systemName: "person.fill")
                                .font(.system(size: 12))
                                .foregroundColor(.gray)
                        )

                    // Plus sign
                    Image(systemName: "plus")
                        .font(.system(size: 10, weight: .bold))
                        .foregroundColor(.white.opacity(0.7))

                    // Future baby
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    accentColor.opacity(0.9),
                                    accentColor.opacity(0.7)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 24, height: 24)
                        .overlay(
                            Image(systemName: "heart.fill")
                                .font(.system(size: 10))
                                .foregroundColor(.white)
                        )
                        .shadow(color: accentColor.opacity(0.4), radius: 2, x: 0, y: 1)
                }
            }

        case .beauty:
            VStack(spacing: 8) {
                Text("Beauty Enhancement")
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(.white.opacity(0.9))

                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(colors: [
                                    accentColor.opacity(0.8),
                                    accentColor.opacity(0.6)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 70, height: 70)
                        .shadow(color: accentColor.opacity(0.4), radius: 4, x: 0, y: 2)

                    VStack {
                        Image(systemName: "face.smiling.fill")
                            .foregroundColor(.white)
                            .font(.system(size: 20))
                        Text("Perfect")
                            .font(.system(size: 9, weight: .bold))
                            .foregroundColor(.white)
                    }
                }
            }
        }
    }
}

// MARK: - Preview

struct PrototypeMainView_Previews: PreviewProvider {
    static var previews: some View {
        PrototypeMainView()
    }
}
