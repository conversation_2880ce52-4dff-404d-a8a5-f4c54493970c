//
//  PrototypeMainView.swift
//  Lmini
//
//  Created by MacBook Pro-Leo on 5/30/25.
//

import SwiftUI

struct PrototypeMainView: View {
    @State private var selectedFeature: FeatureType?
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 标题
                    VStack(alignment: .leading, spacing: 8) {
                        Text("AI Photo Enhancement")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)
                        
                        Text("Transform your photos with AI")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal)
                    .padding(.top, 20)
                    
                    // 功能卡片
                    LazyVGrid(columns: [
                        GridItem(.flexible(), spacing: 16),
                        GridItem(.flexible(), spacing: 16)
                    ], spacing: 16) {
                        FeatureCard(
                            feature: .enhance,
                            title: "Enhance your photos' quality.",
                            backgroundColor: Color.pink.opacity(0.3),
                            accentColor: Color.pink
                        ) {
                            selectedFeature = .enhance
                        }
                        
                        FeatureCard(
                            feature: .anime,
                            title: "Your 90s anime version.",
                            backgroundColor: Color.teal.opacity(0.3),
                            accentColor: Color.teal
                        ) {
                            selectedFeature = .anime
                        }
                        
                        FeatureCard(
                            feature: .future,
                            title: "See your future family.",
                            backgroundColor: Color.orange.opacity(0.3),
                            accentColor: Color.orange
                        ) {
                            selectedFeature = .future
                        }
                        
                        FeatureCard(
                            feature: .beauty,
                            title: "Get the best face in every photo.",
                            backgroundColor: Color.blue.opacity(0.3),
                            accentColor: Color.blue
                        ) {
                            selectedFeature = .beauty
                        }
                    }
                    .padding(.horizontal)
                    
                    Spacer(minLength: 100)
                }
            }
            .navigationBarHidden(true)
        }
        .fullScreenCover(item: $selectedFeature) { feature in
            FeatureDetailView(feature: feature) {
                selectedFeature = nil
            }
        }
    }
}

// MARK: - Feature Types

enum FeatureType: String, CaseIterable, Identifiable {
    case enhance = "enhance"
    case anime = "anime"
    case future = "future"
    case beauty = "beauty"
    
    var id: String { rawValue }
    
    var title: String {
        switch self {
        case .enhance: return "Photo Enhancement"
        case .anime: return "Anime Style"
        case .future: return "Future Family"
        case .beauty: return "Beauty Mode"
        }
    }
    
    var description: String {
        switch self {
        case .enhance: return "Enhance your photos' quality with AI technology"
        case .anime: return "Transform into your 90s anime version"
        case .future: return "See what your future family might look like"
        case .beauty: return "Get the best face in every photo"
        }
    }
    
    var filterType: AIImageService.FilterType {
        switch self {
        case .enhance: return .enhance
        case .anime: return .anime
        case .future: return .future
        case .beauty: return .beauty
        }
    }
}

// MARK: - Feature Card

struct FeatureCard: View {
    let feature: FeatureType
    let title: String
    let backgroundColor: Color
    let accentColor: Color
    let action: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            // 标题区域
            VStack(alignment: .leading, spacing: 12) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.black)
                    .multilineTextAlignment(.leading)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                Spacer()
            }
            .padding(.top, 20)
            .padding(.horizontal, 16)
            .frame(height: 80)
            
            // 图像预览区域
            ZStack {
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.black)
                    .frame(height: 200)
                
                // 模拟手机界面
                VStack(spacing: 8) {
                    // 顶部状态栏
                    HStack {
                        Circle()
                            .fill(Color.white)
                            .frame(width: 4, height: 4)
                        
                        Spacer()
                        
                        HStack(spacing: 2) {
                            Circle()
                                .fill(Color.white)
                                .frame(width: 3, height: 3)
                            Circle()
                                .fill(Color.white)
                                .frame(width: 3, height: 3)
                        }
                    }
                    .padding(.horizontal, 12)
                    .padding(.top, 8)
                    
                    // 主要内容区域
                    ZStack {
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.gray.opacity(0.3))
                            .frame(height: 140)
                        
                        // 根据功能类型显示不同的预览内容
                        featurePreviewContent
                    }
                    .padding(.horizontal, 8)
                    
                    // 底部控制区域
                    HStack {
                        ForEach(0..<4) { _ in
                            Circle()
                                .fill(Color.white.opacity(0.6))
                                .frame(width: 6, height: 6)
                        }
                    }
                    .padding(.bottom, 8)
                }
                
                // 下载按钮
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        Button(action: {}) {
                            Image(systemName: "arrow.down.circle.fill")
                                .font(.title2)
                                .foregroundColor(.white)
                                .background(Circle().fill(Color.black.opacity(0.3)))
                        }
                        .padding(.trailing, 12)
                        .padding(.bottom, 12)
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
        }
        .background(backgroundColor)
        .clipShape(RoundedRectangle(cornerRadius: 20))
        .onTapGesture {
            action()
        }
    }
    
    @ViewBuilder
    private var featurePreviewContent: some View {
        switch feature {
        case .enhance:
            HStack(spacing: 8) {
                // Before
                VStack {
                    Text("Before")
                        .font(.caption2)
                        .foregroundColor(.white)
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(0.6))
                        .frame(width: 50, height: 60)
                        .overlay(
                            Image(systemName: "photo")
                                .foregroundColor(.white.opacity(0.7))
                                .font(.caption)
                        )
                }
                
                // After
                VStack {
                    Text("After")
                        .font(.caption2)
                        .foregroundColor(.white)
                    RoundedRectangle(cornerRadius: 8)
                        .fill(accentColor.opacity(0.8))
                        .frame(width: 50, height: 60)
                        .overlay(
                            Image(systemName: "sparkles")
                                .foregroundColor(.white)
                                .font(.caption)
                        )
                }
            }
            
        case .anime:
            VStack {
                Text("Anime Style")
                    .font(.caption)
                    .foregroundColor(.white)
                RoundedRectangle(cornerRadius: 8)
                    .fill(accentColor.opacity(0.8))
                    .frame(width: 80, height: 80)
                    .overlay(
                        Image(systemName: "person.crop.circle")
                            .foregroundColor(.white)
                            .font(.title2)
                    )
            }
            
        case .future:
            VStack {
                Text("Future Baby")
                    .font(.caption)
                    .foregroundColor(.white)
                HStack(spacing: 4) {
                    Circle()
                        .fill(Color.white.opacity(0.8))
                        .frame(width: 25, height: 25)
                    Circle()
                        .fill(Color.white.opacity(0.8))
                        .frame(width: 25, height: 25)
                    Circle()
                        .fill(accentColor.opacity(0.8))
                        .frame(width: 20, height: 20)
                }
            }
            
        case .beauty:
            VStack {
                Text("Beauty Mode")
                    .font(.caption)
                    .foregroundColor(.white)
                Circle()
                    .fill(accentColor.opacity(0.8))
                    .frame(width: 60, height: 60)
                    .overlay(
                        Image(systemName: "face.smiling")
                            .foregroundColor(.white)
                            .font(.title3)
                    )
            }
        }
    }
}

// MARK: - Preview

struct PrototypeMainView_Previews: PreviewProvider {
    static var previews: some View {
        PrototypeMainView()
    }
}
