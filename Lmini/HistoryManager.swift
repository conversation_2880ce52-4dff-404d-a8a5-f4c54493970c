//
//  HistoryManager.swift
//  Lmini
//
//  Created by Mac<PERSON>ook Pro-Leo on 5/30/25.
//

import Foundation
import UIKit
import SwiftUI

// MARK: - History Item Model

struct HistoryItem: Identifiable, Codable {
    let id = UUID()
    let timestamp: Date
    let filterType: AIImageService.FilterType
    let originalImageData: Data?
    let processedImageData: Data?
    let processingTime: TimeInterval
    
    init(
        originalImage: UIImage,
        processedImage: UIImage,
        filterType: AIImageService.FilterType,
        processingTime: TimeInterval = 0.0
    ) {
        self.timestamp = Date()
        self.filterType = filterType
        self.originalImageData = originalImage.jpegData(compressionQuality: 0.8)
        self.processedImageData = processedImage.jpegData(compressionQuality: 0.8)
        self.processingTime = processingTime
    }
    
    var originalImage: UIImage? {
        guard let data = originalImageData else { return nil }
        return UIImage(data: data)
    }
    
    var processedImage: UIImage? {
        guard let data = processedImageData else { return nil }
        return UIImage(data: data)
    }
}

// MARK: - History Manager

class HistoryManager: ObservableObject {
    static let shared = HistoryManager()
    
    @Published var historyItems: [HistoryItem] = []
    
    private let maxHistoryItems = 50
    private let userDefaults = UserDefaults.standard
    private let historyKey = "LminiHistoryItems"
    
    private init() {
        loadHistory()
    }
    
    // MARK: - Public Methods
    
    func addHistoryItem(
        originalImage: UIImage,
        processedImage: UIImage,
        filterType: AIImageService.FilterType,
        processingTime: TimeInterval = 0.0
    ) {
        let newItem = HistoryItem(
            originalImage: originalImage,
            processedImage: processedImage,
            filterType: filterType,
            processingTime: processingTime
        )
        
        DispatchQueue.main.async {
            self.historyItems.insert(newItem, at: 0)
            
            // 限制历史记录数量
            if self.historyItems.count > self.maxHistoryItems {
                self.historyItems.removeLast()
            }
            
            self.saveHistory()
        }
    }
    
    func removeHistoryItem(_ item: HistoryItem) {
        DispatchQueue.main.async {
            self.historyItems.removeAll { $0.id == item.id }
            self.saveHistory()
        }
    }
    
    func clearHistory() {
        DispatchQueue.main.async {
            self.historyItems.removeAll()
            self.saveHistory()
        }
    }
    
    func getHistoryItem(by id: UUID) -> HistoryItem? {
        return historyItems.first { $0.id == id }
    }
    
    func getHistoryItems(for filterType: AIImageService.FilterType) -> [HistoryItem] {
        return historyItems.filter { $0.filterType == filterType }
    }
    
    // MARK: - Statistics
    
    var totalProcessedImages: Int {
        return historyItems.count
    }
    
    var averageProcessingTime: TimeInterval {
        guard !historyItems.isEmpty else { return 0.0 }
        let totalTime = historyItems.reduce(0.0) { $0 + $1.processingTime }
        return totalTime / Double(historyItems.count)
    }
    
    var mostUsedFilter: AIImageService.FilterType? {
        let filterCounts = Dictionary(grouping: historyItems, by: { $0.filterType })
            .mapValues { $0.count }
        
        return filterCounts.max(by: { $0.value < $1.value })?.key
    }
    
    // MARK: - Persistence
    
    private func saveHistory() {
        do {
            let data = try JSONEncoder().encode(historyItems)
            userDefaults.set(data, forKey: historyKey)
        } catch {
            print("Failed to save history: \(error)")
        }
    }
    
    private func loadHistory() {
        guard let data = userDefaults.data(forKey: historyKey) else { return }
        
        do {
            let items = try JSONDecoder().decode([HistoryItem].self, from: data)
            DispatchQueue.main.async {
                self.historyItems = items
            }
        } catch {
            print("Failed to load history: \(error)")
        }
    }
    
    // MARK: - Legacy Support (for tests)
    
    var items: [HistoryItem] {
        return historyItems
    }
    
    func addItem(
        original: UIImage,
        enhanced: UIImage,
        processingTime: TimeInterval
    ) {
        addHistoryItem(
            originalImage: original,
            processedImage: enhanced,
            filterType: .enhance,
            processingTime: processingTime
        )
    }
    
    func clearAll() {
        clearHistory()
    }
}

// MARK: - Extensions

extension AIImageService.FilterType: Codable {
    // FilterType 已经是 String 类型的 enum，自动支持 Codable
}

// MARK: - History Statistics View

struct HistoryStatsView: View {
    @ObservedObject var historyManager = HistoryManager.shared
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Statistics")
                .font(.headline)
            
            HStack {
                StatCard(
                    title: "Total Images",
                    value: "\(historyManager.totalProcessedImages)",
                    icon: "photo.stack",
                    color: .blue
                )
                
                StatCard(
                    title: "Avg Time",
                    value: String(format: "%.1fs", historyManager.averageProcessingTime),
                    icon: "clock",
                    color: .green
                )
            }
            
            if let mostUsed = historyManager.mostUsedFilter {
                StatCard(
                    title: "Most Used Filter",
                    value: mostUsed.displayName,
                    icon: mostUsed.icon,
                    color: .purple
                )
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                Spacer()
            }
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Color.white)
        .clipShape(RoundedRectangle(cornerRadius: 8))
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// MARK: - Preview

struct HistoryStatsView_Previews: PreviewProvider {
    static var previews: some View {
        HistoryStatsView()
            .padding()
    }
}
