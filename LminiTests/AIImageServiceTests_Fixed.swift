//
//  AIImageServiceTests.swift
//  LminiTests
//
//  Created by MacBook Pro-Leo on 5/30/25.
//

import XCTest
import UIKit
@testable import Lmini

final class AIImageServiceTests: XCTestCase {

    var service: AIImageService!

    override func setUpWithError() throws {
        service = AIImageService.shared
    }

    override func tearDownWithError() throws {
        service = nil
    }

    func testMockImageEnhancement() throws {
        // 创建测试图片
        let testImage = createTestImage()

        let expectation = XCTestExpectation(description: "Image enhancement completed")
        var resultImage: UIImage?
        var progressUpdates: [Double] = []

        // 测试模拟增强
        service.enhanceImage(
            testImage,
            filterType: .enhance,
            provider: .mock,
            progressCallback: { progress in
                progressUpdates.append(progress)
            },
            completion: { result in
                switch result {
                case .success(let enhanced):
                    resultImage = enhanced
                    expectation.fulfill()
                case .failure(let error):
                    XCTFail("Enhancement failed with error: \(error)")
                }
            }
        )

        wait(for: [expectation], timeout: 10.0)

        // 验证结果
        XCTAssertNotNil(resultImage, "Enhanced image should not be nil")
        XCTAssertFalse(progressUpdates.isEmpty, "Progress updates should not be empty")
        XCTAssertEqual(progressUpdates.last ?? 0.0, 1.0, accuracy: 0.01, "Final progress should be 1.0")
    }

    func testInvalidImageHandling() throws {
        let expectation = XCTestExpectation(description: "Invalid image handling")

        // 创建一个无效的图片（空图片）
        let invalidImage = UIImage()

        service.enhanceImage(
            invalidImage,
            filterType: .enhance,
            provider: .mock,
            progressCallback: { _ in },
            completion: { result in
                switch result {
                case .success(_):
                    // 即使是空图片，模拟服务也应该能处理
                    expectation.fulfill()
                case .failure(let error):
                    // 或者返回错误也是可以接受的
                    XCTAssertTrue(error is AIImageService.EnhancementError)
                    expectation.fulfill()
                }
            }
        )

        wait(for: [expectation], timeout: 5.0)
    }

    func testProgressCallback() throws {
        let testImage = createTestImage()
        let expectation = XCTestExpectation(description: "Progress callback test")
        var progressValues: [Double] = []

        service.enhanceImage(
            testImage,
            filterType: .enhance,
            provider: .mock,
            progressCallback: { progress in
                progressValues.append(progress)
            },
            completion: { _ in
                expectation.fulfill()
            }
        )

        wait(for: [expectation], timeout: 15.0)

        // 验证进度回调
        XCTAssertFalse(progressValues.isEmpty, "Should have progress updates")
        XCTAssertTrue(progressValues.first! >= 0.0, "First progress should be >= 0")
        XCTAssertTrue(progressValues.last! <= 1.0, "Last progress should be <= 1")

        // 验证进度是递增的
        for i in 1..<progressValues.count {
            XCTAssertGreaterThanOrEqual(progressValues[i], progressValues[i-1],
                                       "Progress should be non-decreasing")
        }
    }

    // MARK: - Helper Methods

    private func createTestImage() -> UIImage {
        let size = CGSize(width: 100, height: 100)
        UIGraphicsBeginImageContext(size)
        defer { UIGraphicsEndImageContext() }

        let context = UIGraphicsGetCurrentContext()!
        context.setFillColor(UIColor.blue.cgColor)
        context.fill(CGRect(origin: .zero, size: size))

        return UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
    }
}

// MARK: - History Manager Tests

final class HistoryManagerTests: XCTestCase {

    var historyManager: HistoryManager!

    override func setUpWithError() throws {
        historyManager = HistoryManager()
        // 清空历史记录
        historyManager.clearAll()
    }

    override func tearDownWithError() throws {
        historyManager.clearAll()
        historyManager = nil
    }

    func testAddHistoryItem() throws {
        let originalImage = createTestImage()
        let enhancedImage = createTestImage()
        let processingTime: TimeInterval = 2.5

        XCTAssertEqual(historyManager.items.count, 0, "History should be empty initially")

        historyManager.addItem(
            original: originalImage,
            enhanced: enhancedImage,
            processingTime: processingTime
        )

        XCTAssertEqual(historyManager.items.count, 1, "Should have one history item")

        let item = historyManager.items.first!
        XCTAssertEqual(item.processingTime, processingTime, "Processing time should match")
        XCTAssertNotNil(item.originalImage, "Original image should not be nil")
        XCTAssertNotNil(item.enhancedImage, "Enhanced image should not be nil")
    }

    func testHistoryLimit() throws {
        let originalImage = createTestImage()
        let enhancedImage = createTestImage()

        // 添加超过限制的历史记录
        for i in 0..<55 {
            historyManager.addItem(
                original: originalImage,
                enhanced: enhancedImage,
                processingTime: Double(i)
            )
        }

        XCTAssertEqual(historyManager.items.count, 50, "History should be limited to 50 items")
    }

    func testClearHistory() throws {
        let originalImage = createTestImage()
        let enhancedImage = createTestImage()

        // 添加一些历史记录
        for _ in 0..<5 {
            historyManager.addItem(
                original: originalImage,
                enhanced: enhancedImage,
                processingTime: 1.0
            )
        }

        XCTAssertEqual(historyManager.items.count, 5, "Should have 5 history items")

        historyManager.clearAll()

        XCTAssertEqual(historyManager.items.count, 0, "History should be empty after clearing")
    }

    private func createTestImage() -> UIImage {
        let size = CGSize(width: 50, height: 50)
        UIGraphicsBeginImageContext(size)
        defer { UIGraphicsEndImageContext() }

        let context = UIGraphicsGetCurrentContext()!
        context.setFillColor(UIColor.red.cgColor)
        context.fill(CGRect(origin: .zero, size: size))

        return UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
    }
}
