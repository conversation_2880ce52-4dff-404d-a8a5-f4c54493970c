//
//  AIImageServiceEnhancedTests.swift
//  LminiTests
//
//  Created by MacBook Pro-Leo on 5/30/25.
//

import XCTest
@testable import Lmini

class AIImageServiceEnhancedTests: XCTestCase {
    
    var aiService: AIImageService!
    
    override func setUpWithError() throws {
        aiService = AIImageService.shared
    }
    
    override func tearDownWithError() throws {
        aiService = nil
    }
    
    // MARK: - Progress Callback Tests
    
    func testProgressCallbackFixed() throws {
        let expectation = XCTestExpectation(description: "Progress callback should be called multiple times")
        expectation.expectedFulfillmentCount = 6 // 期望6次进度更新
        
        let testImage = createTestImage()
        var progressValues: [Double] = []
        
        aiService.enhanceImage(
            testImage,
            filterType: .enhance,
            provider: .mock,
            progressCallback: { progress in
                progressValues.append(progress)
                expectation.fulfill()
            },
            completion: { result in
                switch result {
                case .success(_):
                    XCTAssertEqual(progressValues.count, 6, "Should receive 6 progress updates")
                    XCTAssertEqual(progressValues.last, 1.0, "Final progress should be 1.0")
                    XCTAssertTrue(progressValues.allSatisfy { $0 >= 0.0 && $0 <= 1.0 }, "All progress values should be between 0 and 1")
                case .failure(let error):
                    XCTFail("Enhancement should succeed, but failed with: \(error)")
                }
            }
        )
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    // MARK: - New Filter Tests
    
    func testAnimeFilter() throws {
        let expectation = XCTestExpectation(description: "Anime filter should work")
        let testImage = createTestImage()
        
        aiService.enhanceImage(
            testImage,
            filterType: .anime,
            provider: .mock,
            progressCallback: { _ in },
            completion: { result in
                switch result {
                case .success(let enhancedImage):
                    XCTAssertNotNil(enhancedImage)
                    XCTAssertEqual(enhancedImage.size, testImage.size)
                    expectation.fulfill()
                case .failure(let error):
                    XCTFail("Anime filter failed: \(error)")
                }
            }
        )
        
        wait(for: [expectation], timeout: 3.0)
    }
    
    func testBeautyFilter() throws {
        let expectation = XCTestExpectation(description: "Beauty filter should work")
        let testImage = createTestImage()
        
        aiService.enhanceImage(
            testImage,
            filterType: .beauty,
            provider: .mock,
            progressCallback: { _ in },
            completion: { result in
                switch result {
                case .success(let enhancedImage):
                    XCTAssertNotNil(enhancedImage)
                    XCTAssertEqual(enhancedImage.size, testImage.size)
                    expectation.fulfill()
                case .failure(let error):
                    XCTFail("Beauty filter failed: \(error)")
                }
            }
        )
        
        wait(for: [expectation], timeout: 3.0)
    }
    
    func testFutureFilter() throws {
        let expectation = XCTestExpectation(description: "Future filter should work")
        let testImage = createTestImage()
        
        aiService.enhanceImage(
            testImage,
            filterType: .future,
            provider: .mock,
            progressCallback: { _ in },
            completion: { result in
                switch result {
                case .success(let enhancedImage):
                    XCTAssertNotNil(enhancedImage)
                    XCTAssertEqual(enhancedImage.size, testImage.size)
                    expectation.fulfill()
                case .failure(let error):
                    XCTFail("Future filter failed: \(error)")
                }
            }
        )
        
        wait(for: [expectation], timeout: 3.0)
    }
    
    func testArtisticFilter() throws {
        let expectation = XCTestExpectation(description: "Artistic filter should work")
        let testImage = createTestImage()
        
        aiService.enhanceImage(
            testImage,
            filterType: .artistic,
            provider: .mock,
            progressCallback: { _ in },
            completion: { result in
                switch result {
                case .success(let enhancedImage):
                    XCTAssertNotNil(enhancedImage)
                    XCTAssertEqual(enhancedImage.size, testImage.size)
                    expectation.fulfill()
                case .failure(let error):
                    XCTFail("Artistic filter failed: \(error)")
                }
            }
        )
        
        wait(for: [expectation], timeout: 3.0)
    }
    
    // MARK: - Batch Processing Tests
    
    func testBatchProcessing() throws {
        let expectation = XCTestExpectation(description: "Batch processing should work")
        let testImages = [createTestImage(), createTestImage(), createTestImage()]
        var processedCount = 0
        
        aiService.enhanceImages(
            testImages,
            filterType: .enhance,
            provider: .mock,
            progressCallback: { index, progress in
                XCTAssertTrue(index >= 0 && index < testImages.count)
                XCTAssertTrue(progress >= 0.0 && progress <= 1.0)
                if progress == 1.0 {
                    processedCount += 1
                }
            },
            completion: { result in
                switch result {
                case .success(let enhancedImages):
                    XCTAssertEqual(enhancedImages.count, testImages.count)
                    XCTAssertEqual(processedCount, testImages.count)
                    expectation.fulfill()
                case .failure(let error):
                    XCTFail("Batch processing failed: \(error)")
                }
            }
        )
        
        wait(for: [expectation], timeout: 10.0)
    }
    
    // MARK: - Filter Type Tests
    
    func testAllFilterTypes() throws {
        let testImage = createTestImage()
        let allFilters = AIImageService.FilterType.allCases
        let expectation = XCTestExpectation(description: "All filters should work")
        expectation.expectedFulfillmentCount = allFilters.count
        
        for filterType in allFilters {
            aiService.enhanceImage(
                testImage,
                filterType: filterType,
                provider: .mock,
                progressCallback: { _ in },
                completion: { result in
                    switch result {
                    case .success(let enhancedImage):
                        XCTAssertNotNil(enhancedImage)
                        XCTAssertEqual(enhancedImage.size, testImage.size)
                        expectation.fulfill()
                    case .failure(let error):
                        XCTFail("Filter \(filterType) failed: \(error)")
                    }
                }
            )
        }
        
        wait(for: [expectation], timeout: 15.0)
    }
    
    // MARK: - Error Handling Tests
    
    func testInvalidImageHandling() throws {
        let expectation = XCTestExpectation(description: "Invalid image should be handled")
        let invalidImage = UIImage() // 空图像
        
        aiService.enhanceImage(
            invalidImage,
            filterType: .enhance,
            provider: .mock,
            progressCallback: { _ in },
            completion: { result in
                switch result {
                case .success(_):
                    XCTFail("Should fail with invalid image")
                case .failure(let error):
                    XCTAssertEqual(error as? AIImageService.EnhancementError, .invalidImage)
                    expectation.fulfill()
                }
            }
        )
        
        wait(for: [expectation], timeout: 3.0)
    }
    
    // MARK: - Helper Methods
    
    private func createTestImage() -> UIImage {
        let size = CGSize(width: 100, height: 100)
        UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
        UIColor.blue.setFill()
        UIRectFill(CGRect(origin: .zero, size: size))
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image ?? UIImage()
    }
}

// MARK: - Extensions for Testing

extension AIImageService.EnhancementError: Equatable {
    public static func == (lhs: AIImageService.EnhancementError, rhs: AIImageService.EnhancementError) -> Bool {
        switch (lhs, rhs) {
        case (.invalidImage, .invalidImage),
             (.networkError, .networkError),
             (.decodingError, .decodingError),
             (.processingTimeout, .processingTimeout):
            return true
        case (.apiError(let lhsMessage), .apiError(let rhsMessage)):
            return lhsMessage == rhsMessage
        default:
            return false
        }
    }
}
