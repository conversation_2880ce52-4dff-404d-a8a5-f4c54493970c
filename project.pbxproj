// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		F771FDD72DE92AA4008A959B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F771FDC12DE92AA1008A959B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F771FDC82DE92AA1008A959B;
			remoteInfo = Lmini;
		};
		F771FDE12DE92AA4008A959B /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F771FDC12DE92AA1008A959B /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = F771FDC82DE92AA1008A959B;
			remoteInfo = Lmini;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		F771FDC92DE92AA1008A959B /* Lmini.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Lmini.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F771FDD62DE92AA4008A959B /* LminiTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = LminiTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		F771FDE02DE92AA4008A959B /* LminiUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = LminiUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		F771FDCB2DE92AA1008A959B /* Lmini */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Lmini;
			sourceTree = "<group>";
		};
		F771FDD92DE92AA4008A959B /* LminiTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LminiTests;
			sourceTree = "<group>";
		};
		F771FDE32DE92AA4008A959B /* LminiUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LminiUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		F771FDC62DE92AA1008A959B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F771FDD32DE92AA4008A959B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F771FDDD2DE92AA4008A959B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		F771FDC02DE92AA1008A959B = {
			isa = PBXGroup;
			children = (
				F771FDCB2DE92AA1008A959B /* Lmini */,
				F771FDD92DE92AA4008A959B /* LminiTests */,
				F771FDE32DE92AA4008A959B /* LminiUITests */,
				F771FDCA2DE92AA1008A959B /* Products */,
			);
			sourceTree = "<group>";
		};
		F771FDCA2DE92AA1008A959B /* Products */ = {
			isa = PBXGroup;
			children = (
				F771FDC92DE92AA1008A959B /* Lmini.app */,
				F771FDD62DE92AA4008A959B /* LminiTests.xctest */,
				F771FDE02DE92AA4008A959B /* LminiUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		F771FDC82DE92AA1008A959B /* Lmini */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F771FDEA2DE92AA4008A959B /* Build configuration list for PBXNativeTarget "Lmini" */;
			buildPhases = (
				F771FDC52DE92AA1008A959B /* Sources */,
				F771FDC62DE92AA1008A959B /* Frameworks */,
				F771FDC72DE92AA1008A959B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				F771FDCB2DE92AA1008A959B /* Lmini */,
			);
			name = Lmini;
			packageProductDependencies = (
			);
			productName = Lmini;
			productReference = F771FDC92DE92AA1008A959B /* Lmini.app */;
			productType = "com.apple.product-type.application";
		};
		F771FDD52DE92AA4008A959B /* LminiTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F771FDED2DE92AA4008A959B /* Build configuration list for PBXNativeTarget "LminiTests" */;
			buildPhases = (
				F771FDD22DE92AA4008A959B /* Sources */,
				F771FDD32DE92AA4008A959B /* Frameworks */,
				F771FDD42DE92AA4008A959B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F771FDD82DE92AA4008A959B /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				F771FDD92DE92AA4008A959B /* LminiTests */,
			);
			name = LminiTests;
			packageProductDependencies = (
			);
			productName = LminiTests;
			productReference = F771FDD62DE92AA4008A959B /* LminiTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		F771FDDF2DE92AA4008A959B /* LminiUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F771FDF02DE92AA4008A959B /* Build configuration list for PBXNativeTarget "LminiUITests" */;
			buildPhases = (
				F771FDDC2DE92AA4008A959B /* Sources */,
				F771FDDD2DE92AA4008A959B /* Frameworks */,
				F771FDDE2DE92AA4008A959B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				F771FDE22DE92AA4008A959B /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				F771FDE32DE92AA4008A959B /* LminiUITests */,
			);
			name = LminiUITests;
			packageProductDependencies = (
			);
			productName = LminiUITests;
			productReference = F771FDE02DE92AA4008A959B /* LminiUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		F771FDC12DE92AA1008A959B /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					F771FDC82DE92AA1008A959B = {
						CreatedOnToolsVersion = 16.3;
					};
					F771FDD52DE92AA4008A959B = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = F771FDC82DE92AA1008A959B;
					};
					F771FDDF2DE92AA4008A959B = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = F771FDC82DE92AA1008A959B;
					};
				};
			};
			buildConfigurationList = F771FDC42DE92AA1008A959B /* Build configuration list for PBXProject "Lmini" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
				"zh-Hant",
				ja,
				ko,
				es,
				pt,
				ru,
				hi,
				th,
			);
			mainGroup = F771FDC02DE92AA1008A959B;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = F771FDCA2DE92AA1008A959B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				F771FDC82DE92AA1008A959B /* Lmini */,
				F771FDD52DE92AA4008A959B /* LminiTests */,
				F771FDDF2DE92AA4008A959B /* LminiUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		F771FDC72DE92AA1008A959B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F771FDD42DE92AA4008A959B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F771FDDE2DE92AA4008A959B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		F771FDC52DE92AA1008A959B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F771FDD22DE92AA4008A959B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F771FDDC2DE92AA4008A959B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		F771FDD82DE92AA4008A959B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F771FDC82DE92AA1008A959B /* Lmini */;
			targetProxy = F771FDD72DE92AA4008A959B /* PBXContainerItemProxy */;
		};
		F771FDE22DE92AA4008A959B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = F771FDC82DE92AA1008A959B /* Lmini */;
			targetProxy = F771FDE12DE92AA4008A959B /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		F771FDE82DE92AA4008A959B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 36BC458JJ5;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		F771FDE92DE92AA4008A959B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 36BC458JJ5;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		F771FDEB2DE92AA4008A959B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 36BC458JJ5;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "Lmini needs access to your camera to take photos for AI enhancement.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "Lmini needs permission to save enhanced photos to your photo library.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Lmini needs access to your photo library to select photos for AI enhancement.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.macbookpro-leo.Lmini";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		F771FDEC2DE92AA4008A959B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 36BC458JJ5;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "Lmini needs access to your camera to take photos for AI enhancement.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "Lmini needs permission to save enhanced photos to your photo library.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Lmini needs access to your photo library to select photos for AI enhancement.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 17.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.macbookpro-leo.Lmini";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		F771FDEE2DE92AA4008A959B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 36BC458JJ5;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.macbookpro-leo.LminiTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Lmini.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Lmini";
			};
			name = Debug;
		};
		F771FDEF2DE92AA4008A959B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 36BC458JJ5;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.macbookpro-leo.LminiTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Lmini.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Lmini";
			};
			name = Release;
		};
		F771FDF12DE92AA4008A959B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 36BC458JJ5;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.macbookpro-leo.LminiUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Lmini;
			};
			name = Debug;
		};
		F771FDF22DE92AA4008A959B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 36BC458JJ5;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.macbookpro-leo.LminiUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Lmini;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		F771FDC42DE92AA1008A959B /* Build configuration list for PBXProject "Lmini" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F771FDE82DE92AA4008A959B /* Debug */,
				F771FDE92DE92AA4008A959B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F771FDEA2DE92AA4008A959B /* Build configuration list for PBXNativeTarget "Lmini" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F771FDEB2DE92AA4008A959B /* Debug */,
				F771FDEC2DE92AA4008A959B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F771FDED2DE92AA4008A959B /* Build configuration list for PBXNativeTarget "LminiTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F771FDEE2DE92AA4008A959B /* Debug */,
				F771FDEF2DE92AA4008A959B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F771FDF02DE92AA4008A959B /* Build configuration list for PBXNativeTarget "LminiUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F771FDF12DE92AA4008A959B /* Debug */,
				F771FDF22DE92AA4008A959B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = F771FDC12DE92AA1008A959B /* Project object */;
}
